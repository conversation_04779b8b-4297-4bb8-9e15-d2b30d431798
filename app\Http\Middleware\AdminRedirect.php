<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdminRedirect
{
    /**
     * Handle an incoming request.
     * This middleware redirects authenticated admin users away from public routes to dashboard
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // If user is authenticated and is admin, redirect to dashboard
        if (Auth::check()) {
            $user = Auth::user();
            
            if ($user->role === '1') {
                return redirect()->route('dashboard')->with('info', 'Admin users should use the dashboard.');
            }
        }

        return $next($request);
    }
}
