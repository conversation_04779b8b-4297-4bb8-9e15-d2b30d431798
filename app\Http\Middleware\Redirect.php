<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class Redirect
{
    public function handle(Request $request, Closure $next, $guard = null)
    {
        if (Auth::guard($guard)->check()) {
            $user = Auth::user();

            // If user is admin, redirect to dashboard
            if ($user && $user->role === '1') {
                return redirect()->route('dashboard')->with('info', 'You are already logged in as admin.');
            }

            // If user is regular user, redirect to home but allow access to public routes
            // This middleware is mainly for guest routes like login/register
        }

        return $next($request);
    }
}
