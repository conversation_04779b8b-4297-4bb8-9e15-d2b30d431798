<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="apple-touch-icon" sizes="96x96" href="<?php echo e(asset('assets/images/logo.png')); ?>">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <link rel="icon" type="image/png" href="<?php echo e(asset('assets/images/logo.png')); ?>">
    <title>
        <?php echo e(env('APP_NAME')); ?>

    </title>

    <?php if(Auth::check() && Auth::user()->role === '1'): ?>
        
        <?php echo $__env->make('layouts.styles.auth.style', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php else: ?>
        
        <?php echo $__env->make('layouts.styles.guest.style', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php endif; ?>

    <?php echo $__env->yieldPushContent('css'); ?>

    <style>
        * {
            font-family: 'Nunito', sans-serif;
            box-sizing: border-box;
        }

        .bdr {
            color: #ffffff !important;
            border-radius: 4px !important;
            background-color: #67748e !important;
        }

        .bdck {
            border-radius: 4px !important;
            pointer-events: none !important;
        }

        body.swal2-shown:not(.swal2-no-backdrop, .swal2-toast-shown) {
            overflow: inherit;
        }

        .ttt {
            color: #67748e !important;
        }

        .btn-success {
            background-color: #67748e !important;
            border-color: #67748e !important;
            border-radius: 4px !important;
        }

        .btn-secondary {
            border-radius: 4px !important;
            background-color: #8392AB !important;
        }

        .ttl{
            color: #67748E !important;
            font-weight: 600;
        }
    </style>
</head>

<body class="<?php echo e($class ?? ''); ?> g-sidenav-show bg-gray-100">
    <?php if(Auth::check() && Auth::user()->role === '1'): ?>
        
        <div class="position-absolute top-0 start-0 w-100" style="height: 296px; z-index: -1;  background-color: #67748e;">
            
        </div>

        <?php echo $__env->make('layouts.navbars.auth.sidenav', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <main class="main-content border-radius-lg">
            <?php echo $__env->yieldContent('content'); ?>
        </main>

        <?php echo $__env->make('components.auth-toast', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <?php echo $__env->make('components.fixed-plugin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php else: ?>
        
        <?php echo $__env->make('components.guest-toast', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <?php echo $__env->make('layouts.navbars.guest.navbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <?php echo $__env->yieldContent('content'); ?>

        
            <?php echo $__env->make('layouts.footers.guest.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        
    <?php endif; ?>

    <?php if(Auth::check() && Auth::user()->role === '1'): ?>
        
        <?php echo $__env->make('layouts.scripts.auth.script', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php else: ?>
        
        <?php echo $__env->make('layouts.scripts.guest.script', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php endif; ?>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            const toggleBtn = document.getElementById("iconNavbarSidenav");
            const sidebar = document.getElementById("sidenav-main");

            if (toggleBtn && sidebar) {
                toggleBtn.addEventListener("click", function() {
                    sidebar.classList.toggle("d-none");
                    sidebar.classList.toggle("d-block");
                });
            }
        });
    </script>
    <?php echo $__env->yieldPushContent('js'); ?>
</body>

</html>
<?php /**PATH C:\xampp\htdocs\trash\resources\views/layouts/app.blade.php ENDPATH**/ ?>