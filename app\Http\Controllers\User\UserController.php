<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;

class UserController extends Controller
{
    /**
     * Show user dashboard
     */
    public function dashboard()
    {
        $user = Auth::user();
        
        // Get user-specific data here
        // For example: bookings, orders, etc.
        
        return view('user.dashboard', compact('user'));
    }

    /**
     * Show user profile
     */
    public function profile()
    {
        $user = Auth::user();
        return view('user.profile', compact('user'));
    }

    /**
     * Update user profile
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        $validatedData = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'mobile' => ['nullable', 'string', 'max:20'],
            'current_password' => ['nullable', 'required_with:new_password'],
            'new_password' => [
                'nullable',
                'confirmed',
                Password::min(8)->mixedCase()->letters()->numbers()->symbols()->max(20)
            ],
        ]);

        // Update basic info
        $user->name = $validatedData['name'];
        $user->email = $validatedData['email'];
        $user->mobile = $validatedData['mobile'] ?? $user->mobile;

        // Update password if provided
        if (!empty($validatedData['current_password']) && !empty($validatedData['new_password'])) {
            if (!Hash::check($validatedData['current_password'], $user->password)) {
                return back()->withErrors(['current_password' => 'Current password is incorrect.']);
            }
            
            $user->password = Hash::make($validatedData['new_password']);
        }

        $user->save();

        return back()->with('success', 'Profile updated successfully!');
    }

    /**
     * Show user bookings (placeholder)
     */
    public function bookings()
    {
        $user = Auth::user();
        
        // Get user bookings here
        // $bookings = Booking::where('user_id', $user->id)->get();
        
        return view('user.bookings', compact('user'));
    }
}
