<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class GuestOnly
{
    /**
     * Handle an incoming request.
     * This middleware ensures only guests (non-authenticated users) can access routes like login/register
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // If user is authenticated, redirect them based on their role
        if (Auth::check()) {
            $user = Auth::user();
            
            // If admin, redirect to dashboard
            if ($user->role === '1') {
                return redirect()->route('dashboard')->with('info', 'You are already logged in as admin.');
            }
            
            // If regular user, redirect to home
            return redirect()->route('home')->with('info', 'You are already logged in.');
        }

        return $next($request);
    }
}
