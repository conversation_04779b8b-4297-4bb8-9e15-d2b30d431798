<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="apple-touch-icon" sizes="96x96" href="{{ asset('assets/images/logo.png') }}">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link rel="icon" type="image/png" href="{{ asset('assets/images/logo.png') }}">
    <title>
        {{ env('APP_NAME') }}
    </title>

    @if(Auth::check() && Auth::user()->role === '1')
        {{-- Admin styles --}}
        @include('layouts.styles.auth.style')
    @else
        {{-- Guest and regular user styles --}}
        @include('layouts.styles.guest.style')
    @endif

    @stack('css')

    <style>
        * {
            font-family: 'Nunito', sans-serif;
            box-sizing: border-box;
        }

        .bdr {
            color: #ffffff !important;
            border-radius: 4px !important;
            background-color: #67748e !important;
        }

        .bdck {
            border-radius: 4px !important;
            pointer-events: none !important;
        }

        body.swal2-shown:not(.swal2-no-backdrop, .swal2-toast-shown) {
            overflow: inherit;
        }

        .ttt {
            color: #67748e !important;
        }

        .btn-success {
            background-color: #67748e !important;
            border-color: #67748e !important;
            border-radius: 4px !important;
        }

        .btn-secondary {
            border-radius: 4px !important;
            background-color: #8392AB !important;
        }

        .ttl{
            color: #67748E !important;
            font-weight: 600;
        }
    </style>
</head>

<body class="{{ $class ?? '' }} g-sidenav-show bg-gray-100">
    @if(Auth::check() && Auth::user()->role === '1')
        {{-- Admin Layout --}}
        <div class="position-absolute top-0 start-0 w-100" style="height: 296px; z-index: -1;  background-color: #67748e;">
            {{-- #5e72e4 --}}
        </div>

        @include('layouts.navbars.auth.sidenav')

        <main class="main-content border-radius-lg">
            @yield('content')
        </main>

        @include('components.auth-toast')

        @include('components.fixed-plugin')
    @else
        {{-- Guest and Regular User Layout --}}
        @include('components.guest-toast')

        @include('layouts.navbars.guest.navbar')

        @yield('content')

        {{-- @unless (Route::is('login')) --}}
            @include('layouts.footers.guest.footer')
        {{-- @endunless --}}
    @endif

    @if(Auth::check() && Auth::user()->role === '1')
        {{-- Admin scripts --}}
        @include('layouts.scripts.auth.script')
    @else
        {{-- Guest and regular user scripts --}}
        @include('layouts.scripts.guest.script')
    @endif
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            const toggleBtn = document.getElementById("iconNavbarSidenav");
            const sidebar = document.getElementById("sidenav-main");

            if (toggleBtn && sidebar) {
                toggleBtn.addEventListener("click", function() {
                    sidebar.classList.toggle("d-none");
                    sidebar.classList.toggle("d-block");
                });
            }
        });
    </script>
    @stack('js')
</body>

</html>
