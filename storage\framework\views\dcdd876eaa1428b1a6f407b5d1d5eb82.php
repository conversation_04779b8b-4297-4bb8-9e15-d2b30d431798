 <!--   Core JS Files   -->
 <script src="<?php echo e(asset('assets/js/core/popper.min.js')); ?>"></script>
 <script src="<?php echo e(asset('assets/js/core/bootstrap.min.js')); ?>"></script>
 <script src="<?php echo e(asset('assets/js/plugins/perfect-scrollbar.min.js')); ?>"></script>
 <script src="<?php echo e(asset('assets/js/plugins/smooth-scrollbar.min.js')); ?>"></script>
 <script>
     var win = navigator.platform.indexOf('Win') > -1;
     if (win && document.querySelector('#sidenav-scrollbar')) {
         var options = {
             damping: '0.5'
         }
         Scrollbar.init(document.querySelector('#sidenav-scrollbar'), options);
     }

     document.addEventListener('DOMContentLoaded', function() {
         setTimeout(function() {
             const alerts = document.querySelectorAll('.alert');
             alerts.forEach(alert => {
                 bootstrap.Alert.getOrCreateInstance(alert).close();
             });
         }, 5000);
     });
 </script>

 <!-- Github buttons -->
 <script async defer src="<?php echo e(asset('assets/script/buttons.js')); ?>"></script>
<?php /**PATH C:\xampp\htdocs\trash\resources\views/layouts/scripts/auth/script.blade.php ENDPATH**/ ?>