<?php

use App\Http\Middleware\Redirect;
use App\Http\Middleware\AdminRole;
use App\Http\Middleware\UserRole;
use App\Http\Middleware\GuestOnly;
use App\Http\Middleware\AdminRedirect;
use App\Exceptions\ThrottleException;
use Illuminate\Foundation\Application;
use Illuminate\Auth\AuthenticationException;
use App\Exceptions\UnauthenticatedException;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Exceptions\ThrottleRequestsException;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        api: __DIR__ . '/../routes/api.php',
        commands: __DIR__ . '/../routes/console.php',
        channels: __DIR__ . '/../routes/channels.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'redirect' => Redirect::class,
            'admin.role' => AdminRole::class,
            'user.role' => UserRole::class,
            'guest.only' => GuestOnly::class,
            'admin.redirect' => AdminRedirect::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->map(AuthenticationException::class, function ($exception) {
            throw new UnauthenticatedException();
        });
        $exceptions->map(ThrottleRequestsException::class, function ($exception) {
            throw new ThrottleException();
        });
    })->create();
