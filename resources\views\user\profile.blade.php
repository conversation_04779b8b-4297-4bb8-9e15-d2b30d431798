@extends('layouts.app')

@section('content')
    <section class="profile-section">
        <div class="container">
            <div class="profile-header">
                <h1>My Profile</h1>
                <p>Manage your account settings and subscription</p>
            </div>

            <div class="profile-container">
                <!-- Profile Information Section -->
                <div class="profile-card">
                    <div class="card-header">
                        <h2><i class="fas fa-user"></i> Profile Information</h2>
                    </div>

                    <form id="profile-form" class="profile-form">
                        <!-- Profile Image Section -->
                        <div class="profile-image-section">
                            <div class="profile-image-container">
                                <div class="profile-image-wrapper">
                                    <img src="{{ asset('assets/images/logo.png') }}" alt="Profile Picture" id="profileImage"
                                        class="profile-image">
                                    <div class="image-overlay">
                                        <i class="fas fa-camera"></i>
                                        <span>Click to Upload</span>
                                    </div>
                                </div>
                                <input type="file" id="imageUpload" accept="image/*" style="display: none;">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="fullName">Full Name *</label>
                                <div class="input-group">
                                    <i class="fas fa-user input-icon"></i>
                                    <input type="text" id="fullName" name="fullName" value="John Doe" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="email">Email Address</label>
                                <div class="input-group">
                                    <i class="fas fa-envelope input-icon"></i>
                                    <input type="email" id="email" name="email" value="<EMAIL>"
                                        readonly>
                                </div>
                                <!-- <small class="field-note">Email cannot be changed for security reasons</small> -->
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="mobile">Mobile Number *</label>
                            <div class="input-group">
                                <i class="fas fa-phone input-icon"></i>
                                <input type="tel" id="mobile" name="mobile" value="+****************" required>
                            </div>
                        </div>

                        <!-- Password Section -->
                        <div class="password-section">
                            <!-- Default Password Display -->
                            <div class="password-display" id="passwordDisplay">
                                <div class="form-group">
                                    <label for="password">Password</label>
                                    <div class="input-group">
                                        <i class="fas fa-lock input-icon"></i>
                                        <input type="password" id="password" name="password" value="MySecurePassword123"
                                            readonly>
                                        <button type="button" class="password-toggle-view" id="togglePasswordView">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="password-action">
                                    <button type="button" class="btn btn-secondary change-password-btn"
                                        id="changePasswordBtn">
                                        <i class="fas fa-edit"></i>
                                        Change Password
                                    </button>
                                </div>
                            </div>

                            <!-- Password Change Form (Hidden by default) -->
                            <div class="password-change-form" id="passwordChangeForm" style="display: none;">
                                <div class="form-group">
                                    <label for="currentPassword">Current Password *</label>
                                    <div class="input-group">
                                        <i class="fas fa-lock input-icon"></i>
                                        <input type="password" id="currentPassword" name="currentPassword"
                                            placeholder="Enter current password" required>
                                        <button type="button" class="password-toggle" id="toggleCurrentPassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="newPassword">New Password *</label>
                                        <div class="input-group">
                                            <i class="fas fa-lock input-icon"></i>
                                            <input type="password" id="newPassword" name="newPassword"
                                                placeholder="Enter new password" required>
                                            <button type="button" class="password-toggle" id="toggleNewPassword">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="confirmPassword">Confirm New Password *</label>
                                        <div class="input-group">
                                            <i class="fas fa-lock input-icon"></i>
                                            <input type="password" id="confirmPassword" name="confirmPassword"
                                                placeholder="Confirm new password" required>
                                            <button type="button" class="password-toggle" id="toggleConfirmPassword">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary" id="updateProfileBtn">
                                <i class="fas fa-save"></i>
                                Update Profile
                            </button>
                            <button type="button" class="btn btn-secondary" id="cancelBtn">
                                <i class="fas fa-times"></i>
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Subscription Section -->
                <div class="subscription-card">
                    <div class="card-header">
                        <h2><i class="fas fa-credit-card"></i> Subscription Details</h2>
                    </div>

                    <div class="subscription-info">
                        <!-- Current Plan -->
                        <div class="current-plan">
                            <div class="plan-badge popular">
                                <i class="fas fa-star"></i>
                                Most Popular
                            </div>

                            <div class="plan-header">
                                <h3>Premium Plan</h3>
                                <div class="plan-price">
                                    <span class="price">$29.99</span>
                                    <span class="duration">/month</span>
                                </div>
                            </div>

                            <div class="plan-status active">
                                <i class="fas fa-check-circle"></i>
                                Active
                            </div>

                            <!-- Features and Details Row -->
                            <div class="features-details-row">
                                <div class="plan-features">
                                    <h4>Plan Features:</h4>
                                    <ul>
                                        <li><i class="fas fa-check"></i> Weekly trash collection</li>
                                        <li><i class="fas fa-check"></i> Recycling bin service</li>
                                        <li><i class="fas fa-check"></i> Holiday schedule management</li>
                                        <li><i class="fas fa-check"></i> SMS notifications</li>
                                        <li><i class="fas fa-check"></i> Priority customer support</li>
                                        <li><i class="fas fa-check"></i> Can cleaning service</li>
                                    </ul>
                                </div>

                                <!-- Subscription Details -->
                                <div class="subscription-details">
                                    <h4>Billing Details:</h4>
                                    <div class="detail-item">
                                        <span class="label">Next Billing Date:</span>
                                        <span class="value">March 15, 2025</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="label">Payment Method:</span>
                                        <span class="value">**** **** **** 1234</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="label">Auto Renewal:</span>
                                        <span class="value status-active">Enabled</span>
                                    </div>
                                </div>
                            </div>

                            <div class="plan-description">
                                <p>Perfect for busy families who want comprehensive trash management with premium features
                                    and priority support.</p>
                            </div>

                            {{-- <div class="subscription-actions">
                                <button class="btn btn-secondary">
                                    <i class="fas fa-edit"></i>
                                    Change Plan
                                </button>
                                <button class="btn btn-danger">
                                    <i class="fas fa-times"></i>
                                    Cancel Subscription
                                </button>
                            </div> --}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="{{ asset('assets/js/profile.js') }}"></script>
@endsection
