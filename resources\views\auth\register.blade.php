@extends('layouts.app')

@section('content')
    <section class="auth-section">
        <div class="container">
            <div class="auth-container">
                <div class="auth-card">
                    <div class="auth-header">
                        <h2>Create Account</h2>
                        <p>Join us to start managing your trash collection service</p>
                    </div>

                    <form id="register-form" class="auth-form" method="POST" action="{{ route('register.post') }}">
                        @csrf

                        {{-- Display general errors --}}
                        @if ($errors->any())
                            <div class="auth-message auth-message-error">
                                <ul class="mb-0">
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        <div class="form-group">
                            <label for="name">Full Name *</label>
                            <div class="input-group">
                                <i class="fas fa-user input-icon"></i>
                                <input type="text" id="name" name="name" value="{{ old('name') }}" required
                                    placeholder="Enter your full name" class="@error('name') is-invalid @enderror">
                            </div>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="email">Email Address *</label>
                            <div class="input-group">
                                <i class="fas fa-envelope input-icon"></i>
                                <input type="email" id="email" name="email" value="{{ old('email') }}" required
                                    placeholder="Enter your email" class="@error('email') is-invalid @enderror">
                            </div>
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="mobile">Mobile Number (Optional)</label>
                            <div class="input-group">
                                <i class="fas fa-phone input-icon"></i>
                                <input type="text" id="mobile" name="mobile" value="{{ old('mobile') }}"
                                    placeholder="Enter your mobile number" class="@error('mobile') is-invalid @enderror">
                            </div>
                            @error('mobile')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="password">Password *</label>
                            <div class="input-group">
                                <i class="fas fa-lock input-icon"></i>
                                <input type="password" id="password" name="password" required
                                    placeholder="Create a password" class="@error('password') is-invalid @enderror">
                                <button type="button" class="password-toggle" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="password-requirements">
                                <small>Password must be at least 8 characters with mixed case, numbers, and symbols</small>
                            </div>
                            @error('password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="password_confirmation">Confirm Password *</label>
                            <div class="input-group">
                                <i class="fas fa-lock input-icon"></i>
                                <input type="password" id="password_confirmation" name="password_confirmation" required
                                    placeholder="Confirm your password" class="@error('password_confirmation') is-invalid @enderror">
                                <button type="button" class="password-toggle" id="toggleConfirmPassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            @error('password_confirmation')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-options">
                            <label class="checkbox-label">
                                <input type="checkbox" name="terms" id="terms" value="1" required
                                    class="@error('terms') is-invalid @enderror" {{ old('terms') ? 'checked' : '' }}>
                                <span class="checkbox-custom"></span>
                                I agree to the <a href="{{ route('terms') }}" target="_blank">Terms & Conditions</a> and <a
                                    href="{{ route('privacy') }}" target="_blank">Privacy Policy</a>
                            </label>
                            @error('terms')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-options">
                            <label class="checkbox-label">
                                <input type="checkbox" name="newsletter" id="newsletter" value="1"
                                    {{ old('newsletter') ? 'checked' : '' }}>
                                <span class="checkbox-custom"></span>
                                Subscribe to our newsletter for service updates
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary btn-large auth-submit">
                            <i class="fas fa-user-plus"></i>
                            Create Account
                        </button>

                        <div class="auth-divider">
                            <span class="bg-white">Already have an account? <a href="{{ route('login') }}" class="">Sign
                                    In</a></span>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
@endsection
